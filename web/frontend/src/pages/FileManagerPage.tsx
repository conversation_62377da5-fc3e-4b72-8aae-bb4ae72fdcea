import { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  HiArrowUpOnSquareStack,
  HiTrash,
  HiXMark,
  HiArrowsRightLeft,
  HiEye,
  HiFolder,
} from "react-icons/hi2";
import {
  getFilesByBucket,
  listBuckets,
  uploadFiles,
  deleteFile,
} from "../api/filesApi";
import {
  setConvertItems,
  setLoading,
  selectFolders,
  updateFileStatus,
  selectConvertItems,
} from "../redux/fileManagerSlice";
import {
  selectIsUploading,
  selectPersistModal,
  setShowModal,
  selectUploadFiles,
} from "../redux/uploadSlice";
import { store } from "../redux/store";
import LocationMenu from "../components/file-manager/LocationMenu";
import ConvertItemTable from "../components/file-manager/table/ConvertItemTable";
import DeleteFilesModal from "../components/file-manager/modal/DeleteFilesModal";
import DeleteMultipleFilesModal from "../components/file-manager/modal/DeleteMultipleFilesModal";
import PreviewModal from "../components/file-manager/modal/PreviewModal";
import UploadModal from "../components/upload/UploadModal";
import { BucketInfo, FileInfo } from "../types/files";
import { toast } from "react-toastify";
import Loading from "../components/common/Loading";
import "./FileManagerPage.css";

const FileManagerPage = () => {
  const dispatch = useDispatch();
  const isUploading = useSelector(selectIsUploading);
  const persistModal = useSelector(selectPersistModal);
  const uploadFilesState = useSelector(selectUploadFiles);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [reload, setReload] = useState<boolean>(false);
  const [buckets, setBuckets] = useState<BucketInfo[]>([]);
  const [selectedBucket, setSelectedBucket] = useState<string>("");
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [location, setLocation] = useState<string>("/");

  const [deletingFile, setDeletingFile] = useState<FileInfo | null>(null);
  const [previewFile, setPreviewFile] = useState<FileInfo | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<FileInfo[]>([]);
  const [isDeletingMultiple, setIsDeletingMultiple] = useState<boolean>(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch buckets on component mount
  useEffect(() => {
    const fetchBuckets = async () => {
      try {
        const bucketList = await listBuckets();
        setBuckets(bucketList);
        if (bucketList.length > 0) {
          setSelectedBucket(bucketList[0].name);
        }
      } catch (error) {
        console.error("Error fetching buckets:", error);
        toast.error("Failed to load buckets");
      }
    };
    fetchBuckets();
  }, []);

  // Fetch files when bucket or location changes
  useEffect(() => {
    const fetchFiles = async () => {
      if (!selectedBucket) return;

      setIsLoading(true);
      try {
        const fileList = await getFilesByBucket(selectedBucket);
        setFiles(fileList);
      } catch (error) {
        console.error("Error fetching files:", error);
        toast.error("Failed to load files");
      } finally {
        setIsLoading(false);
      }
    };

    fetchFiles();
  }, [selectedBucket, location, reload]);

  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0 || !selectedBucket) return;

    try {
      const result = await uploadFiles(Array.from(files), location, selectedBucket);
      toast.success(result.message);
      setReload(!reload);

      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      console.error("Upload failed:", error);
      toast.error("Failed to upload files");
    }
  };

  const handleDeleteFile = async (file: FileInfo) => {
    try {
      await deleteFile(file.id, file.bucket_id);
      toast.success("File deleted successfully");
      setReload(!reload);
    } catch (error) {
      console.error("Delete failed:", error);
      toast.error("Failed to delete file");
    }
  };

  const handleSelectFiles = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleShowUploadDetails = () => {
    dispatch(setShowModal(true));
  };

  const handleBucketChange = (bucketName: string) => {
    setSelectedBucket(bucketName);
    setLocation("/");
    setSelectedFiles([]);
  };

  return (
    <div className="file-manager-page">
      <div className="header">
        <div className="header-left">
        <h1>File Manager</h1>
          <select
            value={selectedBucket}
            onChange={(e) => handleBucketChange(e.target.value)}
            className="bucket-selector"
          >
            <option value="">Select Bucket</option>
            {(buckets ?? []).map((bucket) => (
              <option key={bucket.id} value={bucket.name}>
                {bucket.name}
              </option>
            ))}
          </select>
        </div>
        <div className="header-right">
          <button
            onClick={handleSelectFiles}
            className="upload-button"
            disabled={isUploading || !selectedBucket}
          >
            <HiArrowUpOnSquareStack className="button-icon" />
            Upload Files
          </button>

          {(isUploading || uploadFilesState.length > 0) && (
            <button
              onClick={handleShowUploadDetails}
              className="upload-detail-button"
            >
              <HiEye className="button-icon" />
              Upload Details
            </button>
          )}
        </div>
      </div>

      <input
        type="file"
        ref={fileInputRef}
        onChange={(e) => handleFileUpload(e.target.files)}
        multiple
        accept="video/*"
        style={{ display: "none" }}
      />

      <div className="content-area">
        {selectedBucket ? (
          <>
            <div className="file-list">
              {isLoading ? (
                <Loading />
              ) : (
                <table className="files-table">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Size</th>
                      <th>Type</th>
                      <th>Uploaded</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {(files ?? []).map((file) => (
                      <tr key={file.id}>
                        <td>{file.name}</td>
                        <td>{formatBytes(file.size)}</td>
                        <td>{file.content_type}</td>
                        <td>{new Date(file.uploaded_at).toLocaleString()}</td>
                        <td>
              <button
                            onClick={() => window.open(file.url, "_blank")}
                            className="action-button"
              >
                            <HiEye />
              </button>
              <button
                            onClick={() => handleDeleteFile(file)}
                            className="action-button delete"
              >
                            <HiTrash />
              </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </>
        ) : (
          <div className="no-bucket-selected">
            <HiFolder className="bucket-icon" />
            <h2>Select a Bucket</h2>
            <p>Choose a bucket from the dropdown menu above to view files</p>
          </div>
        )}
      </div>

      {/* Modals */}
      <DeleteFilesModal
        file={deletingFile}
        isOpen={!!deletingFile}
        onClose={() => setDeletingFile(null)}
        onSuccess={() => setReload(!reload)}
      />

      <PreviewModal
        previewFile={previewFile}
        isOpen={!!previewFile}
        onClose={() => setPreviewFile(null)}
      />

      <DeleteMultipleFilesModal
        files={selectedFiles}
        isOpen={isDeletingMultiple}
        onClose={() => {
          setIsDeletingMultiple(false);
        }}
        onSuccess={() => {
          setReload(!reload);
          setSelectedFiles([]);
        }}
      />

      <UploadModal />
    </div>
  );
};

// Helper function to format bytes
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export default FileManagerPage;
