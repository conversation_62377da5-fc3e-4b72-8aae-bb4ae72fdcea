import { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  HiArrowUpOnSquareStack,
  HiTrash,
  HiXMark,
  HiArrowsRightLeft,
  HiEye,
  HiFolder,
  HiInformationCircle,
} from "react-icons/hi2";
import {
  getFilesByBucket,
  listBuckets,
  uploadFiles as apiUploadFiles,
  deleteFileFromBucket,
  deleteMultipleFilesFromBucket
} from "../api/filesApi";
import {
  setConvertItems,
  setLoading,
  selectFolders,
  updateFileStatus,
  selectConvertItems,
} from "../redux/fileManagerSlice";
import {
  selectIsUploading,
  selectPersistModal,
  setShowModal,
  selectUploadFiles,
} from "../redux/uploadSlice";
import { store } from "../redux/store";
import LocationMenu from "../components/file-manager/LocationMenu";
import ConvertItemTable from "../components/file-manager/table/ConvertItemTable";
import CreateFolderModal from "../components/file-manager/modal/CreateFolderModal";
import MoveFilesModal from "../components/file-manager/modal/MoveFilesModal";
import PreviewModal from "../components/file-manager/modal/PreviewModal";
import RenameFileModal from "../components/file-manager/modal/RenameFileModal";
import UploadModal from "../components/upload/UploadModal";
import { ConvertItem, FileStatusUpdate, BucketInfo, FileInfo } from "../types/files";
import { toast } from "react-toastify";
import webSocketService from "../api/websocket";
import Loading from "../components/common/Loading";
import "./FileManagerPage.css";

// Helper function to convert FileInfo to ConvertItem format
const convertFileInfoToConvertItem = (fileInfo: FileInfo, bucketName: string): ConvertItem => {
  return {
    id: parseInt(fileInfo.id.replace(/[^0-9]/g, '') || '0'), // Extract numeric part for ID
    filename: fileInfo.name,
    location: "/", // Backblaze doesn't have nested folders in the same way
    duration: 0, // Not available in FileInfo
    status: 0, // Default to success for uploaded files
    size: fileInfo.size,
    storage_type: 1, // S3/Backblaze storage type
    created_at: fileInfo.uploaded_at,
    updated_at: fileInfo.uploaded_at,
    c_location: "/",
    name: fileInfo.name,
    description: "",
    episode: "",
    width: undefined,
    height: undefined,
    fps: undefined,
    video_codec: undefined,
    audio_codec: undefined,
    bitrate: undefined,
    recorder_id: undefined,
    codec_settings_version: undefined,
    // Store Backblaze-specific info for delete operations
    bucket_id: fileInfo.bucket_id,
    backblaze_file_id: fileInfo.id, // Store original file ID for deletion
  } as ConvertItem & { bucket_id: string; backblaze_file_id: string };
};

const FileManagerPage = () => {
  const dispatch = useDispatch();
  const folders = useSelector(selectFolders);
  const isUploading = useSelector(selectIsUploading);
  const persistModal = useSelector(selectPersistModal);
  const uploadFiles = useSelector(selectUploadFiles);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [reload, setReload] = useState<boolean>(false);
  const [location, setLocation] = useState<string>("/");
  const [buckets, setBuckets] = useState<BucketInfo[]>([]);
  const [selectedBucket, setSelectedBucket] = useState<string>("");

  const [isShowCreateFolderModal, setIsShowCreateFolderModal] =
    useState<boolean>(false);
  const [deletingFile, setDeletingFile] = useState<ConvertItem | null>(null);
  const [previewFile, setPreviewFile] = useState<ConvertItem | null>(null);
  const [renamingFile, setRenamingFile] = useState<ConvertItem | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<ConvertItem[]>([]);
  const [isDeletingMultiple, setIsDeletingMultiple] = useState<boolean>(false);
  const [isMovingFiles, setIsMovingFiles] = useState<boolean>(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch buckets on component mount
  useEffect(() => {
    const fetchBuckets = async () => {
      try {
        const bucketList = await listBuckets();
        setBuckets(bucketList);
        if (bucketList.length > 0) {
          setSelectedBucket(bucketList[0].name);
        }
      } catch (error) {
        console.error("Error fetching buckets:", error);
        toast.error("Failed to load buckets");
      }
    };
    fetchBuckets();
  }, []);

  // Fetch files when bucket changes
  useEffect(() => {
    const fetchConvertItems = async () => {
      if (!selectedBucket) return;

      console.log(
        "FileManagerPage: Starting to fetch files for bucket:",
        selectedBucket
      );
      setIsLoading(true);
      dispatch(setLoading(true));

      try {
        const response = await getFilesByBucket(selectedBucket);

        // Convert FileInfo to ConvertItem format
        const convertedItems = response.map(fileInfo => convertFileInfoToConvertItem(fileInfo, selectedBucket));

        console.log(
          "FileManagerPage: Fetched files:",
          convertedItems?.length || 0,
          "from bucket:",
          selectedBucket
        );

        dispatch(
          setConvertItems({
            items: convertedItems,
            folders: [], // Backblaze doesn't have folders in the same way
          })
        );
      } catch (error) {
        console.error("Error fetching files:", error);
        toast.error("Failed to load files");
      } finally {
        setIsLoading(false);
        dispatch(setLoading(false));
      }
    };

    fetchConvertItems();
  }, [selectedBucket, reload, dispatch]);

  // Handle modal persistence when returning to file manager during uploads
  useEffect(() => {
    if (persistModal && (isUploading || uploadFiles.length > 0)) {
      dispatch(setShowModal(true));
    }
  }, [persistModal, isUploading, dispatch]);

  // Subscribe to WebSocket file status updates
  useEffect(() => {
    // Subscribe to file status updates
    const unsubscribe = webSocketService.subscribeToFileUpdates(
      (update: FileStatusUpdate) => {
        console.log("Received file status update:", update);

        // Update the file status in the Redux store
        dispatch(
          updateFileStatus({
            id: update.id,
            status: update.status,
          })
        );

        // Find the file in the current items to get its name
        const items = selectConvertItems(store.getState());
        const file = items.find((item: ConvertItem) => item.id === update.id);
        const fileName = file
          ? file.name || file.filename
          : `File #${update.id}`;

        // Show a notification with appropriate message based on status
        switch (update.status) {
          case 0: // Success
            toast.success(`${fileName} was transcoded successfully.`);
            break;
          case 1: // Queue
            toast.info(`${fileName} is queued for transcoding.`);
            break;
          case 2: // Failed
            toast.error(`${fileName} transcoding has failed.`);
            break;
          case 3: // Transcoding
            toast.info(`${fileName} is now being transcoded.`);
            break;
          default:
            toast.info(`${fileName} status has changed.`);
        }
      }
    );

    // Cleanup on unmount
    return () => {
      unsubscribe();
    };
  }, [dispatch]);

  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0 || !selectedBucket) return;

    try {
      // Use the Backblaze upload API directly instead of uploadService
      const result = await apiUploadFiles(Array.from(files), location, selectedBucket);
      toast.success(result.message);
      setReload(!reload);

      // Reset the file input to allow uploading the same file again
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      console.error("Upload failed:", error);
      toast.error("Failed to upload files");
    }
  };

  const handleSelectFiles = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleShowUploadDetails = () => {
    dispatch(setShowModal(true));
  };

  const handleBucketChange = (bucketName: string) => {
    setSelectedBucket(bucketName);
    setLocation("/");
    setSelectedFiles([]);
  };

  // Handle single file deletion for Backblaze
  const handleDeleteFile = async (file: ConvertItem & { bucket_id?: string; backblaze_file_id?: string }) => {
    if (!selectedBucket) return;

    try {
      // Use the original file name from Backblaze
      const fileName = file.backblaze_file_id || file.filename;
      await deleteFileFromBucket(fileName, selectedBucket);
      toast.success("File deleted successfully");
      setReload(!reload);
    } catch (error) {
      console.error("Delete failed:", error);
      toast.error("Failed to delete file");
    }
  };

  // Handle multiple file deletion for Backblaze
  const handleDeleteMultipleFiles = async (files: ConvertItem[]) => {
    if (!selectedBucket) return;

    try {
      const filesToDelete = files.map(file => ({
        name: (file as any).backblaze_file_id || file.filename,
        bucketName: selectedBucket
      }));

      const result = await deleteMultipleFilesFromBucket(filesToDelete);

      if (result.success) {
        toast.success(`Successfully deleted ${files.length} file(s)`);
      } else {
        toast.error(`Failed to delete ${result.failedCount} file(s)`);
        result.errors.forEach(error => console.error(error));
      }

      setReload(!reload);
      setSelectedFiles([]);
    } catch (error) {
      console.error("Delete failed:", error);
      toast.error("Failed to delete files");
    }
  };

  const addFolder = (name: string) => {
    setLocation([location === "/" ? "" : location, name].join("/"));
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      await handleFileUpload(e.dataTransfer.files);
      // Clear the dataTransfer object to allow the same files to be dropped again
      e.dataTransfer.clearData();
    }
  };

  return (
    <div className="file-manager-page">
      <div className="header">
        <div className="header-left">
          <h1>File Manager</h1>
          <select
            value={selectedBucket}
            onChange={(e) => handleBucketChange(e.target.value)}
            className="bucket-selector"
          >
            <option value="">Select Bucket</option>
            {(buckets ?? []).map((bucket) => (
              <option key={bucket.id} value={bucket.name}>
                {bucket.name}
              </option>
            ))}
          </select>
        </div>
        <div className="header-right">
          <button
            onClick={handleSelectFiles}
            className="upload-button"
            disabled={isUploading || !selectedBucket}
          >
            <HiArrowUpOnSquareStack className="button-icon" />
            Upload Files
          </button>

          {(isUploading || uploadFiles.length > 0) && (
            <button
              onClick={handleShowUploadDetails}
              className="upload-detail-button"
            >
              <HiEye className="button-icon" />
              Upload Details
            </button>
          )}
        </div>
      </div>

      <input
        type="file"
        ref={fileInputRef}
        onChange={(e) => handleFileUpload(e.target.files)}
        multiple
        accept="video/*"
        style={{ display: "none" }}
      />

      {/* Main content area */}
      <div className="content-area">
        {selectedBucket ? (
          <>
            {/* Location navigation */}
            <div className="location-menu-container">
              <LocationMenu
                location={location}
                setLocation={setLocation}
                setIsShowCreateFolderModal={setIsShowCreateFolderModal}
              />
            </div>

            {/* Upload area */}
            <div className="upload-container">
              <div
                onDragOver={handleDragOver}
                onDrop={handleDrop}
                onClick={isUploading ? undefined : handleSelectFiles}
                className="upload-area"
                style={{
                  opacity: isUploading ? 0.7 : 1,
                  cursor: isUploading ? "default" : "pointer",
                }}
              >
                <div className="upload-content">
                  <HiArrowUpOnSquareStack className="upload-icon" />
                  <p className="upload-text">
                    <span className="upload-highlight">
                      {isUploading ? "Upload in progress..." : "Click to upload"}
                    </span>
                    {!isUploading && " or drag and drop video files"}
                  </p>
                  <p className="upload-subtext">
                    {isUploading
                      ? "Please wait while files are being processed"
                      : "MP4, MOV, AVI, MKV up to 2GB"}
                  </p>
                </div>
              </div>
            </div>

            {/* Action bar - only visible when files are selected */}
            {selectedFiles.length > 0 && (
              <div className="action-bar">
                <div className="selected-count">
                  {selectedFiles.length} file(s) selected
                </div>
                <div className="action-bar-buttons">
                  <button
                    onClick={() => setSelectedFiles([])}
                    className="action-bar-button cancel-button"
                    title="Cancel selection"
                  >
                    <HiXMark className="button-icon" />
                    Cancel
                  </button>
                  <button
                    onClick={() => setIsMovingFiles(true)}
                    className="action-bar-button move-button"
                    title="Move selected files"
                  >
                    <HiArrowsRightLeft className="button-icon" />
                    Move
                  </button>
                  <button
                    onClick={() => setIsDeletingMultiple(true)}
                    className="action-bar-button delete-button"
                    title="Delete selected files"
                  >
                    <HiTrash className="button-icon" />
                    Delete
                  </button>
                </div>
              </div>
            )}

            {/* File table */}
            <ConvertItemTable
              isLoading={isLoading}
              location={location}
              setLocation={setLocation}
              setDeletingFile={setDeletingFile}
              setPreviewFile={setPreviewFile}
              setRenamingFile={setRenamingFile}
              selectedFiles={selectedFiles}
              setSelectedFiles={setSelectedFiles}
            />
          </>
        ) : (
          <div className="no-bucket-selected">
            <HiFolder className="bucket-icon" />
            <h2>Select a Bucket</h2>
            <p>Choose a bucket from the dropdown menu above to view files</p>
          </div>
        )}
      </div>

      {isLoading && !isUploading && <Loading />}

      {/* Modals */}
      <CreateFolderModal
        addFolder={addFolder}
        isOpen={isShowCreateFolderModal}
        onClose={() => setIsShowCreateFolderModal(false)}
        folders={folders}
      />

      {/* Custom delete modal for single file */}
      {deletingFile && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Delete File</h2>
              <button className="close-button" onClick={() => setDeletingFile(null)}>
                <HiXMark />
              </button>
            </div>
            <div className="modal-body">
              <div className="delete-message">
                <p className="delete-question">
                  Are you sure you want to delete the file?
                </p>
                <div className="delete-filename">
                  {deletingFile.filename}
                </div>
                <div className="delete-warning">
                  <HiInformationCircle className="warning-icon" />
                  <span>This will permanently remove the file from Backblaze storage</span>
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button
                className="modal-button cancel-button"
                onClick={() => setDeletingFile(null)}
              >
                Cancel
              </button>
              <button
                className="modal-button delete-button"
                onClick={() => {
                  handleDeleteFile(deletingFile as any);
                  setDeletingFile(null);
                }}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      <PreviewModal
        previewFile={previewFile}
        isOpen={!!previewFile}
        onClose={() => setPreviewFile(null)}
      />

      <RenameFileModal
        file={renamingFile}
        isOpen={!!renamingFile}
        onClose={() => setRenamingFile(null)}
        onSuccess={() => setReload(!reload)}
      />

      {/* Custom delete modal for multiple files */}
      {isDeletingMultiple && selectedFiles.length > 0 && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Delete Files</h2>
              <button className="close-button" onClick={() => setIsDeletingMultiple(false)}>
                <HiXMark />
              </button>
            </div>
            <div className="modal-body">
              <div className="delete-message">
                <p className="delete-question">
                  Are you sure you want to delete {selectedFiles.length} file(s)?
                </p>
                <div className="delete-files-list">
                  {selectedFiles.slice(0, 5).map((file, index) => (
                    <div key={index} className="delete-filename">
                      {file.filename}
                    </div>
                  ))}
                  {selectedFiles.length > 5 && (
                    <div className="delete-more-files">
                      ...and {selectedFiles.length - 5} more file(s)
                    </div>
                  )}
                </div>
                <div className="delete-warning">
                  <HiInformationCircle className="warning-icon" />
                  <span>This will permanently remove the files from Backblaze storage</span>
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button
                className="modal-button cancel-button"
                onClick={() => setIsDeletingMultiple(false)}
              >
                Cancel
              </button>
              <button
                className="modal-button delete-button"
                onClick={() => {
                  handleDeleteMultipleFiles(selectedFiles);
                  setIsDeletingMultiple(false);
                }}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      <MoveFilesModal
        files={selectedFiles}
        isOpen={isMovingFiles}
        currentLocation={location}
        onClose={() => {
          setIsMovingFiles(false);
        }}
        onSuccess={(destinationLocation) => {
          // If a specific destination location is provided, navigate to it
          if (destinationLocation && destinationLocation !== location) {
            // Clear selected files before changing location
            setSelectedFiles([]);
            // Navigate to the destination location
            setLocation(destinationLocation);
          } else {
            // Otherwise just reload the current location
            setReload(!reload);
            // Clear selected files
            setSelectedFiles([]);
          }
        }}
      />

      {/* Upload Modal - shows detailed upload progress */}
      <UploadModal />
    </div>
  );
};

export default FileManagerPage;
